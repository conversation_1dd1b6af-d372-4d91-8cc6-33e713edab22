<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="实体类型" prop="schema">
        <el-select
          v-model="queryParams.schema"
          placeholder="请选择实体类型"
          clearable
          filterable
          style="width: 200px"
          @change="handleQuery"
        >
          <el-option
            v-for="option in schemaOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="国家" prop="country">
        <el-input
          v-model="queryParams.country"
          placeholder="请输入国家"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实体名称" prop="caption">
        <el-input
          v-model="queryParams.caption"
          placeholder="请输入实体名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['data:openSanctions:kyb:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="kybList" @selection-change="handleSelectionChange">
      <el-table-column label="序号" align="center" type="index" width="60"/>
      <el-table-column label="实体名称" align="center" prop="caption" width="200" show-overflow-tooltip />
      <el-table-column label="实体类型" align="center" prop="schema" width="120" />
      <el-table-column label="数据集" align="center" prop="datasets" width="150" show-overflow-tooltip />
      <el-table-column label="国家" align="center" prop="country" width="100" />
      <el-table-column label="姓名" align="center" prop="name" width="150" show-overflow-tooltip />
      <el-table-column label="创建日期" align="center" prop="createdat" width="180" />
      <el-table-column label="别名" align="center" prop="alias" width="150" show-overflow-tooltip />
      <el-table-column label="主题" align="center" prop="topics" width="120" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status" width="100" />
      <el-table-column label="税务识别号码" align="center" prop="taxnumber" width="150" show-overflow-tooltip />
      <el-table-column label="来源链接" align="center" prop="sourceurl" width="200" show-overflow-tooltip />
      <el-table-column label="部门" align="center" prop="authority" width="150" show-overflow-tooltip />
      <el-table-column label="监管计划或制裁清单" align="center" prop="program" width="200" show-overflow-tooltip />
      <el-table-column label="原因" align="center" prop="reason" width="150" show-overflow-tooltip />
      <el-table-column label="注册号码" align="center" prop="registrationnumber" width="150" show-overflow-tooltip />
      <el-table-column label="地址" align="center" prop="address" width="200" show-overflow-tooltip />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script setup name="KybList">
import { listKyb, exportKyb, getSchemaOptions } from "@/api/data/openSanctions/kyb";
import { onMounted, ref, reactive, toRefs, getCurrentInstance } from "vue";

const { proxy } = getCurrentInstance();

const kybList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const schemaOptions = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    schema: undefined,
    country: undefined,
    name: undefined,
    caption: undefined
  },
});

const { queryParams } = toRefs(data);

/** 查询了解您的企业(KYB)数据集列表 */
function getList() {
  loading.value = true;
  listKyb(queryParams.value).then(response => {
    kybList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取实体类型选项 */
function getSchemaOptionsList() {
  getSchemaOptions().then(response => {
    if (response && response.code === 200) {
      schemaOptions.value = response.data;
    }
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  // 处理多选逻辑
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('data/openSanctions/kyb/export', {
    ...queryParams.value
  }, `KYB数据集_${proxy.parseTime(new Date(), '{y}-{m}-{d}')}.xlsx`);
}

// 页面初始化时加载所有选项数据
onMounted(() => {
  // 加载列表数据
  getList();
  getSchemaOptionsList();
});
</script>
