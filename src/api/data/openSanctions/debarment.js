import request from '@/utils/request'

// 查询被禁止的公司和个人列表
export function listDebarment(query) {
  return request({
    url: '/data/openSanctions/debarment/list',
    method: 'get',
    params: query
  })
}

// 导出被禁止的公司和个人
export function exportDebarment(query) {
  return request({
    url: '/data/openSanctions/debarment/export',
    method: 'get',
    params: query
  })
}

// 获取实体类型选项
export function getSchemaOptions() {
  return request({
    url: '/data/openSanctions/debarment/schemaOptions',
    method: 'get'
  })
}
