import request from '@/utils/request'

// 查询开放所有权数据集列表
export function listOpenownership(query) {
  return request({
    url: '/data/openSanctions/openownership/list',
    method: 'get',
    params: query
  })
}

// 导出开放所有权数据集
export function exportOpenownership(query) {
  return request({
    url: '/data/openSanctions/openownership/export',
    method: 'get',
    params: query
  })
}

// 获取实体类型选项
export function getSchemaOptions() {
  return request({
    url: '/data/openSanctions/openownership/schemaOptions',
    method: 'get'
  })
}
