import request from '@/utils/request'

// 查询OpenSanctions默认列表
export function listDefault(query) {
  return request({
    url: '/data/openSanctions/default/list',
    method: 'get',
    params: query
  })
}

// 导出OpenSanctions默认
export function exportDefault(query) {
  return request({
    url: '/data/openSanctions/default/export',
    method: 'get',
    params: query
  })
}

// 获取实体类型选项
export function getSchemaOptions() {
  return request({
    url: '/data/openSanctions/default/schemaOptions',
    method: 'get'
  })
}
