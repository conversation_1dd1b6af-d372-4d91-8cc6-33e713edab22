import request from '@/utils/request'

// 查询监管观察名单列表
export function listRegulatory(query) {
  return request({
    url: '/data/openSanctions/regulatory/list',
    method: 'get',
    params: query
  })
}

// 导出监管观察名单
export function exportRegulatory(query) {
  return request({
    url: '/data/openSanctions/regulatory/export',
    method: 'get',
    params: query
  })
}

// 获取实体类型选项
export function getSchemaOptions() {
  return request({
    url: '/data/openSanctions/regulatory/schemaOptions',
    method: 'get'
  })
}
