import request from '@/utils/request'

// 查询政治公众人物(PEP)列表
export function listPeps(query) {
  return request({
    url: '/data/openSanctions/peps/list',
    method: 'get',
    params: query
  })
}

// 导出政治公众人物(PEP)
export function exportPeps(query) {
  return request({
    url: '/data/openSanctions/peps/export',
    method: 'get',
    params: query
  })
}

// 获取实体类型选项
export function getSchemaOptions() {
  return request({
    url: '/data/openSanctions/peps/schemaOptions',
    method: 'get'
  })
}
