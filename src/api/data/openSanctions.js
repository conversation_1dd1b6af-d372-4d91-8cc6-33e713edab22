import request from '@/utils/request'

// 了解您的企业(KYB)数据集
export function listKyb(query) {
  return request({
    url: '/data/openSanctions/kyb/list',
    method: 'get',
    params: query
  })
}

export function exportKyb(query) {
  return request({
    url: '/data/openSanctions/kyb/export',
    method: 'get',
    params: query
  })
}

// 被禁止的公司和个人
export function listDebarment(query) {
  return request({
    url: '/data/openSanctions/debarment/list',
    method: 'get',
    params: query
  })
}

export function exportDebarment(query) {
  return request({
    url: '/data/openSanctions/debarment/export',
    method: 'get',
    params: query
  })
}

// 开放所有权数据集
export function listOpenownership(query) {
  return request({
    url: '/data/openSanctions/openownership/list',
    method: 'get',
    params: query
  })
}

export function exportOpenownership(query) {
  return request({
    url: '/data/openSanctions/openownership/export',
    method: 'get',
    params: query
  })
}

// 政治公众人物(PEP)
export function listPeps(query) {
  return request({
    url: '/data/openSanctions/peps/list',
    method: 'get',
    params: query
  })
}

export function exportPeps(query) {
  return request({
    url: '/data/openSanctions/peps/export',
    method: 'get',
    params: query
  })
}

// 监管观察名单
export function listRegulatory(query) {
  return request({
    url: '/data/openSanctions/regulatory/list',
    method: 'get',
    params: query
  })
}

export function exportRegulatory(query) {
  return request({
    url: '/data/openSanctions/regulatory/export',
    method: 'get',
    params: query
  })
}

// OpenSanctions默认
export function listDefault(query) {
  return request({
    url: '/data/openSanctions/default/list',
    method: 'get',
    params: query
  })
}

export function exportDefault(query) {
  return request({
    url: '/data/openSanctions/default/export',
    method: 'get',
    params: query
  })
}

// 获取实体类型选项
export function getSchemaOptions() {
  return request({
    url: '/data/openSanctions/schemaOptions',
    method: 'get'
  })
}
